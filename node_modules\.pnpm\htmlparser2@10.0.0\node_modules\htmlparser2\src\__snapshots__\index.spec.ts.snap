// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Index > createDocumentStream 1`] = `
Document {
  "children": [
    &This is text,
    <!-- and comments -->,
    <tags />,
  ],
  "endIndex": null,
  "next": null,
  "parent": null,
  "prev": null,
  "startIndex": null,
  "type": "root",
}
`;

exports[`Index > createDomStream 1`] = `
[
  &This is text,
  <!-- and comments -->,
  <tags />,
]
`;

exports[`Index > parseDOM 1`] = `
[
  <a
    foo=""
  >
    <b>
      <c>
        ProcessingInstruction {
          "data": "?foo",
          "endIndex": null,
          "name": "?foo",
          "next": Yay!,
          "parent": <c>
            [Circular]
            Yay!
          </c>,
          "prev": null,
          "startIndex": null,
          "type": "directive",
        }
        Yay!
      </c>
    </b>
  </a>,
]
`;

exports[`Index > parseDocument 1`] = `
Document {
  "children": [
    <a
      foo=""
    >
      <b>
        <c>
          ProcessingInstruction {
            "data": "?foo",
            "endIndex": null,
            "name": "?foo",
            "next": Yay!,
            "parent": <c>
              [Circular]
              Yay!
            </c>,
            "prev": null,
            "startIndex": null,
            "type": "directive",
          }
          Yay!
        </c>
      </b>
    </a>,
  ],
  "endIndex": null,
  "next": null,
  "parent": null,
  "prev": null,
  "startIndex": null,
  "type": "root",
}
`;
