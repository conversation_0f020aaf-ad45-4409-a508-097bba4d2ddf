#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import fetch from 'node-fetch';
import * as cheerio from 'cheerio';

const server = new McpServer({
  name: "duckduckgo-web-search",
  version: "1.0.0"
});

// DuckDuckGo即时答案搜索
server.registerTool(
  "duckduckgo_search",
  {
    title: "DuckDuckGo Search",
    description: "Search using DuckDuckGo instant answers (completely free, no API key required)",
    inputSchema: { 
      query: z.string().describe("Search query")
    }
  },
  async ({ query }) => {
    try {
      // DuckDuckGo即时答案API
      const response = await fetch(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`);
      const data = await response.json();
      
      let results = [];
      
      // 添加即时答案
      if (data.Answer) {
        results.push({
          type: "instant_answer",
          text: data.Answer,
          source: data.AnswerType
        });
      }
      
      // 添加摘要
      if (data.Abstract) {
        results.push({
          type: "abstract",
          text: data.Abstract,
          source: data.AbstractSource,
          url: data.AbstractURL
        });
      }
      
      // 添加相关主题
      if (data.RelatedTopics && data.RelatedTopics.length > 0) {
        results.push({
          type: "related_topics",
          topics: data.RelatedTopics.slice(0, 5).map(topic => ({
            text: topic.Text,
            url: topic.FirstURL
          }))
        });
      }

      // 如果没有结果，尝试HTML搜索
      if (results.length === 0) {
        const htmlResults = await searchDuckDuckGoHTML(query);
        results = htmlResults;
      }

      return {
        content: [{
          type: "text",
          text: JSON.stringify(results, null, 2)
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `搜索错误: ${error.message}`
        }],
        isError: true
      };
    }
  }
);

// HTML搜索功能（备用方案）
async function searchDuckDuckGoHTML(query) {
  try {
    const response = await fetch(`https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    const html = await response.text();
    const $ = cheerio.load(html);
    
    const results = [];
    $('.result').each((i, element) => {
      if (i >= 10) return false; // 限制10个结果
      
      const title = $(element).find('.result__title a').text().trim();
      const url = $(element).find('.result__title a').attr('href');
      const snippet = $(element).find('.result__snippet').text().trim();
      
      if (title && url) {
        results.push({
          type: "web_result",
          title: title,
          url: url.startsWith('//') ? 'https:' + url : url,
          snippet: snippet
        });
      }
    });
    
    return results;
  } catch (error) {
    return [{
      type: "error",
      text: `HTML搜索失败: ${error.message}`
    }];
  }
}

// 网页内容抓取工具
server.registerTool(
  "fetch_webpage",
  {
    title: "Fetch Webpage Content",
    description: "Extract text content from any webpage",
    inputSchema: { 
      url: z.string().describe("URL to fetch and extract content from")
    }
  },
  async ({ url }) => {
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        timeout: 10000
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const html = await response.text();
      const $ = cheerio.load(html);
      
      // 移除脚本和样式
      $('script, style, nav, footer, aside, .ad, .advertisement').remove();
      
      // 提取主要内容
      let content = '';
      const mainSelectors = ['main', 'article', '.content', '.post', '.entry', 'body'];
      
      for (const selector of mainSelectors) {
        const element = $(selector).first();
        if (element.length > 0) {
          content = element.text();
          break;
        }
      }
      
      if (!content) {
        content = $('body').text();
      }
      
      // 清理文本
      const cleanContent = content
        .replace(/\s+/g, ' ')
        .trim()
        .substring(0, 8000); // 限制长度

      return {
        content: [{
          type: "text",
          text: `网页标题: ${$('title').text()}\n网页URL: ${url}\n\n内容:\n${cleanContent}`
        }]
      };
    } catch (error) {
      return {
        content: [{
          type: "text",
          text: `抓取网页失败: ${error.message}`
        }],
        isError: true
      };
    }
  }
);

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("DuckDuckGo MCP Server started successfully!");
}

main().catch((error) => {
  console.error("Server error:", error);
  process.exit(1);
});
