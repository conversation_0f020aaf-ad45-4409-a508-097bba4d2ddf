# DuckDuckGo MCP Server

完全免费的DuckDuckGo搜索MCP服务器，无需API密钥！

## 功能特性

- ✅ **完全免费** - 无需任何API密钥
- ✅ **DuckDuckGo即时答案** - 获取快速答案
- ✅ **网页搜索** - HTML搜索结果
- ✅ **网页内容抓取** - 提取任意网页内容
- ✅ **智能内容提取** - 自动识别主要内容区域

## 安装步骤

1. 进入项目目录：
```bash
cd duckduckgo-mcp-server
```

2. 安装依赖：
```bash
npm install
```

3. 测试运行：
```bash
npm start
```

## 配置到Gemini

在您的 `settings.json` 中添加：

```json
{
  "mcpServers": {
    "duckduckgo-search": {
      "command": "node",
      "args": ["C:\\path\\to\\duckduckgo-mcp-server\\server.js"]
    }
  }
}
```

## 可用工具

### 1. duckduckgo_search
- 搜索DuckDuckGo获取即时答案、摘要和相关主题
- 如果即时答案API无结果，自动回退到HTML搜索

### 2. fetch_webpage
- 抓取任意网页内容
- 智能提取主要内容区域
- 自动清理和格式化文本

## 使用示例

```javascript
// 搜索示例
{
  "tool": "duckduckgo_search",
  "arguments": {
    "query": "什么是人工智能"
  }
}

// 网页抓取示例
{
  "tool": "fetch_webpage",
  "arguments": {
    "url": "https://example.com"
  }
}
```
