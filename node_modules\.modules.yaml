hoistPattern:
  - '*'
hoistedDependencies:
  accepts@2.0.0:
    accepts: private
  ajv@6.12.6:
    ajv: private
  body-parser@2.2.0:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  cheerio-select@2.1.0:
    cheerio-select: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  cors@2.8.5:
    cors: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@5.2.2:
    css-select: private
  css-what@6.2.2:
    css-what: private
  data-uri-to-buffer@4.0.1:
    data-uri-to-buffer: private
  debug@4.4.1:
    debug: private
  depd@2.0.0:
    depd: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding-sniffer@0.2.1:
    encoding-sniffer: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  eventsource-parser@3.0.3:
    eventsource-parser: private
  eventsource@3.0.7:
    eventsource: private
  express-rate-limit@7.5.1(express@5.1.0):
    express-rate-limit: private
  express@5.1.0:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fetch-blob@3.2.0:
    fetch-blob: private
  finalhandler@2.1.0:
    finalhandler: private
  formdata-polyfill@4.0.10:
    formdata-polyfill: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  htmlparser2@10.0.0:
    htmlparser2: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.6.3:
    iconv-lite: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-promise@4.0.0:
    is-promise: private
  isexe@2.0.0:
    isexe: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  ms@2.1.3:
    ms: private
  negotiator@1.0.0:
    negotiator: private
  node-domexception@1.0.0:
    node-domexception: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-key@3.1.1:
    path-key: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  pkce-challenge@5.0.0:
    pkce-challenge: private
  proxy-addr@2.0.7:
    proxy-addr: private
  punycode@2.3.1:
    punycode: private
  qs@6.14.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  router@2.2.0:
    router: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  statuses@2.0.2:
    statuses: private
  toidentifier@1.0.1:
    toidentifier: private
  type-is@2.0.1:
    type-is: private
  undici@7.13.0:
    undici: private
  unpipe@1.0.0:
    unpipe: private
  uri-js@4.4.1:
    uri-js: private
  vary@1.1.2:
    vary: private
  web-streams-polyfill@3.3.3:
    web-streams-polyfill: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  which@2.0.2:
    which: private
  wrappy@1.0.2:
    wrappy: private
  zod-to-json-schema@3.24.6(zod@3.25.76):
    zod-to-json-schema: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.9.0
pendingBuilds: []
prunedAt: Thu, 31 Jul 2025 11:47:06 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: I:\.pnpm-store\v10
virtualStoreDir: I:\duckduckgo-mcp-server\node_modules\.pnpm
virtualStoreDirMaxLength: 60
