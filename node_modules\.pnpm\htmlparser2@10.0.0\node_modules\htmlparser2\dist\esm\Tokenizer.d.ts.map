{"version": 3, "file": "Tokenizer.d.ts", "sourceRoot": "", "sources": ["../../src/Tokenizer.ts"], "names": [], "mappings": "AAmGA,oBAAY,SAAS;IACjB,OAAO,IAAI;IACX,QAAQ,IAAI;IACZ,MAAM,IAAI;IACV,MAAM,IAAI;CACb;AAED,MAAM,WAAW,SAAS;IACtB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACpD,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACxC,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACtD,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACpD,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IAClE,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAClD,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACpE,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACrD,KAAK,IAAI,IAAI,CAAC;IACd,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACrD,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/D,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACzC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9C,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;CAC3D;AAqBD,MAAM,CAAC,OAAO,OAAO,SAAS;IA6BtB,OAAO,CAAC,QAAQ,CAAC,GAAG;IA5BxB,6CAA6C;IAC7C,OAAO,CAAC,KAAK,CAAc;IAC3B,uBAAuB;IACvB,OAAO,CAAC,MAAM,CAAM;IACpB,iEAAiE;IACjE,OAAO,CAAC,YAAY,CAAK;IACzB,oEAAoE;IACpE,OAAO,CAAC,KAAK,CAAK;IAClB,oCAAoC;IACpC,OAAO,CAAC,WAAW,CAAK;IACxB,kIAAkI;IAClI,OAAO,CAAC,SAAS,CAAc;IAC/B,oEAAoE;IACpE,OAAO,CAAC,SAAS,CAAS;IAC1B,uDAAuD;IAChD,OAAO,UAAQ;IACtB,wCAAwC;IACxC,OAAO,CAAC,MAAM,CAAK;IAEnB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAU;IAClC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAU;IACzC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAgB;gBAG1C,EACI,OAAe,EACf,cAAqB,GACxB,EAAE;QAAE,OAAO,CAAC,EAAE,OAAO,CAAC;QAAC,cAAc,CAAC,EAAE,OAAO,CAAA;KAAE,EACjC,GAAG,EAAE,SAAS;IAU5B,KAAK,IAAI,IAAI;IAWb,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAM1B,GAAG,IAAI,IAAI;IAIX,KAAK,IAAI,IAAI;IAIb,MAAM,IAAI,IAAI;IAOrB,OAAO,CAAC,SAAS;IAejB,OAAO,CAAC,eAAe,CAA0B;IACjD,OAAO,CAAC,aAAa,CAAK;IAC1B,OAAO,CAAC,yBAAyB;IAoBjC,mEAAmE;IACnE,OAAO,CAAC,iBAAiB;IAwCzB,OAAO,CAAC,kBAAkB;IAe1B;;;;;OAKG;IACH,OAAO,CAAC,aAAa;IAkBrB;;;;;;;OAOG;IACH,OAAO,CAAC,kBAAkB;IAwB1B;;;;;OAKG;IACH,OAAO,CAAC,cAAc;IAItB,OAAO,CAAC,YAAY;IAOpB,OAAO,CAAC,kBAAkB;IA6B1B,OAAO,CAAC,cAAc;IAQtB,OAAO,CAAC,yBAAyB;IAYjC,OAAO,CAAC,qBAAqB;IAQ7B,OAAO,CAAC,wBAAwB;IAOhC,OAAO,CAAC,wBAAwB;IAiBhC,OAAO,CAAC,qBAAqB;IAW7B,OAAO,CAAC,oBAAoB;IAQ5B,OAAO,CAAC,uBAAuB;IAc/B,OAAO,CAAC,yBAAyB;IAajC,OAAO,CAAC,sBAAsB;IAkB9B,OAAO,CAAC,iCAAiC;IAGzC,OAAO,CAAC,iCAAiC;IAGzC,OAAO,CAAC,6BAA6B;IAWrC,OAAO,CAAC,sBAAsB;IAW9B,OAAO,CAAC,kBAAkB;IAO1B,OAAO,CAAC,4BAA4B;IAOpC,OAAO,CAAC,kBAAkB;IAW1B,OAAO,CAAC,qBAAqB;IAO7B,OAAO,CAAC,mBAAmB;IAY3B,OAAO,CAAC,mBAAmB;IAyB3B,OAAO,CAAC,WAAW;IAcnB,OAAO,CAAC,aAAa;IAmBrB;;OAEG;IACH,OAAO,CAAC,OAAO;IAoBf,OAAO,CAAC,cAAc;IAItB;;;;OAIG;IACH,OAAO,CAAC,KAAK;IAkHb,OAAO,CAAC,MAAM;IAWd,gCAAgC;IAChC,OAAO,CAAC,kBAAkB;IAkC1B,OAAO,CAAC,aAAa;CAsBxB"}